**MTL (Multi-Tier Learning)** 

## **راهنمای جامع توسعه الگوریتم MTL برای تولید تایم‌لاین هوشمند**

این سند به عنوان یک طرح جامع (Blueprint) برای ساخت یک سیستم پیشنهاد محتوا (Recommendation System) پیشرفته به نام MTL عمل می‌کند. هدف، ارائه تمام جزئیات لازم برای پیاده‌سازی هر لایه از الگوریتم و ادغام آن با یک شبکه اجتماعی جدید است.

### **۱. معماری کلان و جریان داده (High-Level Architecture & Data Flow)**

قبل از ورود به جزئیات لایه‌ها، درک معماری کلی ضروری است. ما یک معماری مبتنی بر میکروسرویس (Microservices) را در نظر می‌گیریم. هر لایه از الگوریتم MTL می‌تواند به عنوان یک سرویس مستقل یا مجموعه‌ای از سرویس‌ها پیاده‌سازی شود. این رویکرد، توسعه، نگهداری و مقیاس‌پذیری سیستم را آسان‌تر می‌کند.

**جریان داده به صورت زیر خواهد بود:**

1.  **Event Stream:** تمام تعاملات کاربر (لایک، کلیک، ری‌پست، اسکرول، زمان مشاهده و غیره) از طریق یک سیستم صف پیام (Message Queue) مانند **Apache Kafka** یا **RabbitMQ** به صورت بلادرنگ دریافت می‌شود.
2.  **Data Lake/Warehouse:** این رویدادها برای پردازش دسته‌ای (Batch Processing) و آموزش مدل‌ها در یک دریاچه داده (مانند AWS S3) یا انبار داده (مانند Google BigQuery) ذخیره می‌شوند.
3.  **Real-time Processing:** یک پردازشگر جریان (Stream Processor) مانند **Apache Flink** یا **Spark Streaming** داده‌ها را برای به‌روزرسانی‌های آنی (مثلاً ویژگی‌های کاربر) پردازش می‌کند.
4.  **Databases:**
      * **Graph Database (Neo4j, Nebula Graph):** برای ذخیره و پردازش گراف چندرفتاری.
      * **Key-Value Store (Redis, Memcached):** برای کشینگ (Caching) سریع ویژگی‌ها، embeddingها، و تایم‌لاین‌های از پیش محاسبه‌شده.
      * **Vector Database (Pinecone, Milvus):** برای جستجوی سریع همسایه‌های نزدیک (ANN) در مرحله تولید کاندیدا.
5.  **Model Serving:** مدل‌های آموزش‌دیده (GBDT, Deep Learning, LLM) از طریق یک سرویس‌دهنده مدل مانند **TensorFlow Serving** یا **TorchServe** برای استنتاج (Inference) بلادرنگ در دسترس قرار می‌گیرند.

 (این یک مثال است، دیاگرام واقعی باید طراحی شود)

-----

### **۲. شرح دقیق لایه‌های الگوریتم MTL**

در این بخش، وظایف، ورودی‌ها، خروجی‌ها و فناوری‌های مورد نیاز برای هر لایه به تفصیل شرح داده می‌شود.

#### **لایه ۱: جمع‌آوری و پیش‌پردازش داده‌ها (Data Ingestion & Preprocessing)**

این لایه، فونداسیون کل سیستم است و وظیفه تامین داده‌های پاک و غنی را بر عهده دارد.

  * **ورودی:** جریان خام رویدادهای کاربر از Kafka. هر رویداد شامل `user_id`, `post_id`, `event_type` (like, comment, scroll, view\_time), `timestamp` و متا‌داده‌های دیگر است.
  * **فرآیند:**
    1.  **مصرف داده (Data Consumption):** یک سرویس Consumer داده‌ها را از تاپیک مربوطه در Kafka می‌خواند.
    2.  **پاکسازی و اعتبارسنجی (Cleaning & Validation):**
          * شناسایی و حذف ترافیک بات‌ها و اسپم‌ها با تحلیل الگوهای رفتاری (مثلاً کلیک‌های بسیار سریع و غیرانسانی).
          * اعتبارسنجی فرمت داده‌ها و حذف رکوردهای ناقص.
    3.  **استخراج ویژگی (Feature Extraction):**
          * **ویژگی‌های محتوا:**
              * **متن:** برای هر پست، متن آن را با استفاده از مدل‌های زبانی از پیش آموزش‌دیده (مانند `BERT` یا `Sentence-Transformers` از کتابخانه **Transformers**) به یک بردار embedding تبدیل کنید. این بردار، معنای семанتیک متن را در خود دارد.
              * **تصویر/ویدیو:** تصاویر و فریم‌های کلیدی ویدیوها را با استفاده از مدل‌های بینایی کامپیوتر (مانند `ResNet` یا `CLIP`) به بردارهای embedding تبدیل کنید.
          * **ویژگی‌های کاربر:** پروفایل کاربر (سن، مکان)، تاریخچه تعاملات اخیر، و ویژگی‌های آماری (مثلاً میانگین تعداد لایک در روز).
          * **ویژگی‌های تعاملی:** ویژگی‌های مربوط به خود تعامل، مانند زمان روز، نوع دستگاه و غیره.
    4.  **ذخیره‌سازی (Storage):**
          * ویژگی‌های استخراج‌شده و embeddingها را در یک پایگاه داده سریع (مانند Redis یا Cassandra) برای دسترسی بلادرنگ ذخیره کنید.
          * داده‌های خام و پردازش‌شده را برای آموزش‌های آفلاین در Data Lake بایگانی کنید.
  * **خروجی:** داده‌های ساختاریافته و پاک که شامل `user_id`, `post_id` و بردارهای ویژگی مربوط به آن‌ها است.
  * **فناوری‌های کلیدی:** `Apache Kafka`, `Apache Flink`, `Pandas`, `Scikit-learn`, `Transformers`, `PyTorch`/`TensorFlow`.

-----

#### **لایه ۲: مدل‌سازی گراف چندرفتاری (Multi-Behavior Graph Embedding)**

قلب درک روابط پیچیده اجتماعی در این لایه قرار دارد. هدف، یادگیری نمایش برداری (embedding) برای کاربران و پست‌ها است که روابط چندگانه بین آن‌ها را در خود جای داده باشد.

  * **ورودی:** داده‌های تعاملی پاک‌شده از لایه ۱ (user-post interactions).
  * **فرآیند:**
    1.  **ساخت گراف ناهمگون (Heterogeneous Graph Construction):**
          * یک گراف با استفاده از کتابخانه **NetworkX** (برای تحلیل) یا **DGL/PyTorch Geometric** (برای مدل‌سازی) بسازید.
          * **گره‌ها (Nodes):** دو نوع گره وجود دارد: `User` و `Post`.
          * **یال‌ها (Edges):** هر نوع تعامل یک نوع یال مجزا است: `(User) -[likes]-> (Post)`, `(User) -[comments_on]-> (Post)`, `(User) -[reposts]-> (Post)`, `(User) -[follows]-> (User)`. هر یال می‌تواند ویژگی‌هایی مانند `timestamp` داشته باشد.
    2.  **مدل‌سازی با گراف عصبی (Graph Neural Network Modeling):**
          * از یک مدل گراف عصبی رابطه‌ای (Relational Graph Convolutional Network - **R-GCN**) یا **Graph Attention Network (GAT)** که برای گراف‌های ناهمگون تطبیق داده شده، استفاده کنید.
          * این مدل یاد می‌گیرد که چگونه اطلاعات را از همسایه‌ها بر اساس نوع رابطه (یال) جمع‌آوری کند. برای مثال، اطلاعاتی که از یک یال `like` می‌آید، وزن و تاثیر متفاوتی نسبت به یک یال `comment` خواهد داشت.
          * مدل به صورت end-to-end آموزش داده می‌شود تا embeddingهایی تولید کند که در یک وظیفه پایین‌دستی (مثلاً پیش‌بینی لینک) عملکرد خوبی داشته باشند.
    3.  **آموزش مدل:** مدل را به صورت دوره‌ای (مثلاً روزانه) با استفاده از داده‌های جدید آموزش دهید. این یک فرآیند Batch است که روی GPUها اجرا می‌شود.
  * **خروجی:** embeddingهای به‌روز شده برای تمام کاربران و پست‌های فعال. این embeddingها در یک Vector Database یا Key-Value Store برای دسترسی سریع ذخیره می‌شوند.
  * **فناوری‌های کلیدی:** `DGL` یا `PyTorch Geometric`, `PyTorch`/`TensorFlow`, `NetworkX`, `Neo4j`.

-----

#### **لایه ۳: رتبه‌بندی پیشرفته (Advanced Ranking)**

این لایه در دو مرحله عمل می‌کند: ابتدا یک استخر بزرگ از کاندیداهای بالقوه ایجاد کرده و سپس آن‌ها را با دقت رتبه‌بندی می‌کند.

  * **ورودی:** `user_id`, embedding کاربر از لایه ۲، و درخواست برای تولید تایم‌لاین.
  * **مرحله الف: تولید کاندیدا (Candidate Generation)**
    1.  **هدف:** از میان میلیون‌ها پست، چند صد تا چند هزار پست مرتبط را به سرعت پیدا کنید. سرعت در اینجا حیاتی است.
    2.  **روش‌ها:**
          * **مبتنی بر Embedding:** با استفاده از embedding کاربر، پست‌هایی را پیدا کنید که embedding آن‌ها در فضای برداری نزدیک‌ترین است. از الگوریتم‌های **Approximate Nearest Neighbor (ANN)** مانند **FAISS** (از فیسبوک) یا **ScaNN** (از گوگل) که در Vector Databaseها پیاده‌سازی شده‌اند، استفاده کنید.
          * **مبتنی بر گراف:** پست‌هایی که توسط کاربرانی که دنبال می‌کنید (Followings) لایک شده یا ری‌پست شده‌اند.
          * **مبتنی بر محبوبیت:** پست‌های پرطرفدار در کل شبکه یا در موضوعات مورد علاقه کاربر.
    3.  **ادغام:** کاندیداهای حاصل از منابع مختلف را با هم ترکیب کرده و дубликат‌ها را حذف کنید.
  * **مرحله ب: رتبه‌بندی اولیه (Initial Ranking)**
    1.  **هدف:** کاندیداهای تولید شده (مثلاً ۱۰۰۰ پست) را بر اساس احتمال تعامل کاربر (لایک، کامنت و غیره) نمره‌دهی و مرتب کنید.
    2.  **ساخت مجموعه ویژگی:** برای هر جفت `(user, candidate_post)` یک بردار ویژگی جامع بسازید:
          * ویژگی‌های embedding کاربر و پست.
          * ویژگی‌های شباهت (مانند حاصل‌ضرب نقطه‌ای embeddingها).
          * ویژگی‌های محتوای پست (طول متن، وجود تصویر/ویدیو).
          * ویژگی‌های نویسنده پست (تعداد دنبال‌کنندگان، نرخ تعامل).
          * ویژگی‌های تعامل اجتماعی (تعداد لایک و ری‌پست اخیر).
    3.  **مدل رتبه‌بندی:** از یک مدل ترکیبی قدرتمند استفاده کنید:
          * **LightGBM (GBDT):** برای یادگیری تعاملات پیچیده بین ویژگی‌های آماری و ساختاری عالی است.
          * **Deep & Cross Network (DCN):** برای یادگیری خودکار تعاملات ویژگی‌ها (feature crosses) در مقیاس بزرگ بهینه است.
          * این دو مدل را می‌توان به صورت موازی اجرا کرد و نمرات آن‌ها را با هم ترکیب نمود (Ensemble).
  * **خروجی:** یک لیست مرتب‌شده از حدود ۲۰۰-۵۰۰ پست برتر با نمره پیش‌بینی تعامل.
  * **فناوری‌های کلیدی:** `FAISS`/`ScaNN`, `LightGBM`, `TensorFlow`/`PyTorch` (برای DCN), `Scikit-learn`.

-----

#### **لایه ۴: ریزرتبه‌بندی با مدل زبان بزرگ (LLM-Powered Reranking)**

این لایه یک فیلتر کیفی و هوشمند است. هدف آن درک عمیق‌تر محتوا و تطبیق آن با سلیقه ظریف کاربر است، کاری که مدل‌های رتبه‌بندی عددی به تنهایی نمی‌توانند انجام دهند.

  * **ورودی:** لیست کوتاه و مرتب‌شده از لایه ۳ (مثلاً ۲۰۰ پست برتر).
  * **فرآیند:**
    1.  **ساخت Prompt:** برای هر پست، یک prompt هوشمند برای LLM (مانند مدل‌های `GPT` یا `Gemini`) بسازید. این prompt شامل:
          * **پروفایل کاربر:** خلاصه‌ای از علایق کاربر ("کاربر به هوش مصنوعی، کمدی، و طبیعت‌گردی علاقه‌مند است").
          * **محتوای پست:** متن کامل پست.
          * **پرسش:** "بر اساس پروفایل کاربر، احتمال اینکه این پست برای او جذاب، باکیفیت و غیرتکراری باشد را از ۱ تا ۱۰ نمره‌دهی کن. همچنین مشخص کن آیا محتوای آن حساس، گمراه‌کننده یا اسپم است؟"
    2.  **استنتاج از LLM:** این promptها را به صورت دسته‌ای (Batch) به سرویس LLM ارسال کنید تا هزینه‌ها و تاخیر کاهش یابد.
    3.  **اعمال نمره جدید:** نمره بازگشتی از LLM را با نمره اصلی از لایه رتبه‌بندی ترکیب کنید (مثلاً با یک میانگین وزنی).
    4.  **اعمال سیاست‌ها:** در این مرحله، قوانین کسب‌وکار را اعمال کنید. مثلاً:
          * کاهش تنوع (Diversity Reduction): نمایش ندادن پست‌های بیش از حد مشابه پشت سر هم.
          * جلوگیری از Viral شدن ناخواسته: محدود کردن نمایش پست‌هایی که به طور افراطی در حال رشد هستند.
  * **خروجی:** یک لیست نهایی و بسیار دقیق از پست‌ها (مثلاً ۱۰۰ پست) که هم از نظر آماری و هم از نظر کیفی بهینه شده‌اند.
  * **فناوری‌های کلیدی:** `OpenAI API` یا `Google AI Platform`, `Transformers`, `LangChain` (برای مدیریت prompt).

-----

#### **لایه ۵: یادگیری تقویتی برای بهینه‌سازی بلندمدت (Reinforcement Learning)**

تایم‌لاین یک فرآیند پویا است. این لایه سیستم را قادر می‌سازد تا از نتایج تصمیمات خود یاد بگیرد و به جای بهینه‌سازی برای کلیک بعدی (کوتاه‌مدت)، رضایت بلندمدت کاربر را به حداکثر برساند.

  * **ورودی:** داده‌های بازخورد بلادرنگ (کاربر پست X را دید، Y را لایک کرد، از Z عبور کرد). لیست کاندیداهای نهایی از لایه ۴.
  * **چارچوب RL:**
      * **Agent:** سیستم پیشنهاددهنده MTL.
      * **State (حالت):** نمایش برداری از وضعیت فعلی کاربر (ترکیبی از embedding کاربر، تاریخچه اخیر تعاملات، و موضوعات مشاهده‌شده در این سشن).
      * **Action (عمل):** انتخاب یک پست خاص از لیست کاندیداها برای نمایش در جایگاه بعدی تایم‌لاین.
      * **Reward (پاداش):** تعریف پاداش بسیار مهم است.
          * **پاداش مثبت:** لایک (+1)، کامنت (+3)، ری‌پست (+5)، زمان مشاهده طولانی (+0.5).
          * **پاداش منفی:** اسکرول سریع و عبور (-0.1)، بلاک کردن کاربر/محتوا (-10).
      * **Policy (سیاست):** یک شبکه عصبی که با ورودی `State`، بهترین `Action` را پیش‌بینی می‌کند.
  * **فرآیند:**
    1.  **آموزش آفلاین (Off-policy):** با استفاده از لاگ‌های تعاملات گذشته، یک مدل RL (مانند **DQN** یا **DDPG**) را آموزش دهید. این روش امن‌تر است زیرا با داده‌های واقعی انجام می‌شود.
    2.  **به‌روزرسانی آنلاین (Online Update):** مدل به صورت دوره‌ای با داده‌های جدید به‌روزرسانی می‌شود تا با تغییر رفتار کاربر تطبیق یابد.
  * **خروجی:** یک سیاست (Policy) به‌روز شده که در لایه رتبه‌بندی یا ریزرتبه‌بندی برای تنظیم نهایی ترتیب پست‌ها استفاده می‌شود.
  * **فناوری‌های کلیدی:** `TF-Agents` یا `Ray RLlib`, `PyTorch`.

-----

#### **لایه ۶: فیلترینگ و ایمنی (Safety & Fairness Filtering)**

این لایه آخرین خط دفاعی برای تضمین یک محیط امن و عادلانه برای کاربران است.

  * **ورودی:** لیست نهایی پست‌ها قبل از نمایش به کاربر.
  * **فرآیند:**
    1.  **فیلترینگ محتوای مضر (Harmful Content Filtering):**
          * استفاده از مدل‌های طبقه‌بندی متن تخصصی (مانند مدل‌های پروژه **Jigsaw** از گوگل) برای شناسایی نفرت‌پراکنی، توهین، و محتوای سمی.
          * استفاده از مدل‌های بینایی کامپیوتر برای شناسایی محتوای خشونت‌آمیز یا بزرگسالان.
    2.  **حذف اسپم و اطلاعات غلط (Spam & Misinformation):**
          * استفاده از مدل‌هایی که برای شناسایی الگوهای اسپم (متن‌های تکراری، لینک‌های مشکوک) و ادعاهای نادرست آموزش دیده‌اند.
    3.  **ارزیابی عدالت (Fairness Evaluation):**
          * اطمینان از اینکه تایم‌لاین به طور ناعادلانه محتوای یک گروه خاص را سرکوب یا ترویج نمی‌کند.
          * بررسی توزیع نمایش محتوا بین نویسندگان مختلف تا از ایجاد "حباب فیلتر" (Filter Bubble) جلوگیری شود.
  * **خروجی:** تایم‌لاین نهایی و پاک‌شده که آماده نمایش به کاربر است.
  * **فناوری‌های کلیدی:** `Jigsaw API`, مدل‌های سفارشی طبقه‌بندی با `Scikit-learn`/`PyTorch`.

-----

### **۳. تابع نهایی و ادغام سیستم**

تمام این لایه‌ها در یک تابع ارکستراتور (Orchestrator) به هم متصل می‌شوند.

```python
# این یک شبه‌کد برای نمایش منطق است

def generate_timeline(user_id: int, top_n: int = 50) -> list[dict]:
    """
    تابع اصلی که با دریافت شناسه کاربر، تایم‌لاین نهایی را تولید می‌کند.
    """
    # ۱. دریافت embedding کاربر (با کشینگ)
    user_embedding = cache.get(f"user_emb_{user_id}")
    if not user_embedding:
        user_embedding = graph_embedding_service.get_user_embedding(user_id)
        cache.set(f"user_emb_{user_id}", user_embedding)

    # ۲. تولید کاندیدا (مثلاً ۱۰۰۰ پست)
    candidate_posts = candidate_generation_service.get_candidates(user_id, user_embedding, count=1000)

    # ۳. رتبه‌بندی اولیه با مدل GBDT + DCN
    ranked_posts = ranking_service.rank(user_id, candidate_posts, count=200)

    # ۴. ریزرتبه‌بندی با LLM (این مرحله می‌تواند به صورت ناهمگام اجرا شود)
    reranked_posts = llm_reranking_service.rerank(user_id, ranked_posts, count=100)

    # ۵. اعمال سیاست RL (تنظیم جزئی ترتیب)
    policy_adjusted_posts = rl_service.apply_policy(user_id, reranked_posts)

    # ۶. فیلترینگ نهایی ایمنی و عدالت
    safe_and_fair_posts = safety_service.filter(policy_adjusted_posts)

    # ۷. بازگرداندن ۵۰ پست برتر و کش کردن نتیجه
    final_timeline = safe_and_fair_posts[:top_n]
    cache.set(f"timeline_{user_id}", final_timeline, ttl=60) # کش برای یک دقیقه

    return final_timeline
```

**نکات کلیدی ادغام:**

  * **ناهمگامی (Asynchronicity):** بسیاری از مراحل مانند استخراج ویژگی یا ریزرتبه‌بندی با LLM می‌توانند به صورت ناهمگام انجام شوند تا زمان پاسخ‌دهی به کاربر کاهش یابد.
  * **کشینگ تهاجمی (Aggressive Caching):** تقریباً خروجی هر لایه قابل کش شدن است. این کار بار محاسباتی را برای کاربران فعال به شدت کاهش می‌دهد.
  * **Fallback Strategy:** در صورت خطا در هر یک از لایه‌های پیچیده (مثلاً سرویس LLM از دسترس خارج شد)، سیستم باید بتواند به یک نسخه ساده‌تر (مثلاً فقط خروجی لایه رتبه‌بندی) بازگردد تا کاربر هرگز با تایم‌لاین خالی مواجه نشود.

-----

### **۴. توضیح خلاصه و ساده از عملکرد الگوریتم MTL**

الگوریتم MTL مانند یک تیم از متخصصان فوق‌هوشمند عمل می‌کند که وظیفه دارند بهترین مجله شخصی را برای شما آماده کنند:

1.  **جمع‌آوری اطلاعات (لایه ۱):** یک دستیار تمام فعالیت‌های شما را زیر نظر دارد: چه مقالاتی را می‌خوانید، کدام‌ها را لایک می‌کنید، و روی کدام‌ها بیشتر مکث می‌کنید.
2.  **درک روابط (لایه ۲):** یک تحلیلگر یک نقشه بزرگ از روابط همه خوانندگان و همه مقالات می‌کشد. او می‌فهمد که طرفداران یک موضوع خاص، به چه موضوعات دیگری هم علاقه دارند.
3.  **پیشنهاد اولیه (لایه ۳):** یک کتابدار تازه‌کار با استفاده از این نقشه، به سرعت یک بغل پر از مقالاتی که *شاید* دوست داشته باشید را برای شما می‌آورد. سپس یک کتابدار ارشد این مقالات را بر اساس محبوبیت، نویسنده و علاقه دوستانتان مرتب می‌کند.
4.  **بررسی کیفی (لایه ۴):** یک ویراستار خبره، ۱۰ مقاله برتر را می‌خواند تا مطمئن شود محتوای آن‌ها واقعاً ارزشمند و جذاب است و صرفاً برای کلیک گرفتن نوشته نشده.
5.  **یادگیری از شما (لایه ۵):** مدیر این تیم (یادگیری تقویتی) مشاهده می‌کند که شما در نهایت کدام مقالات را خواندید و کدام‌ها را رد کردید. او از این اطلاعات استفاده می‌کند تا دفعه بعد کل تیم را آموزش دهد که انتخاب‌های بهتری برای شما داشته باشند.
6.  **فیلتر نهایی (لایه ۶):** در آخر، یک مامور حراست، مجله نهایی را بررسی می‌کند تا هیچ محتوای نامناسب یا مضری در آن وجود نداشته باشد و به شما تحویل داده شود.

این فرآیند چندلایه تضمین می‌کند که تایم‌لاین شما نه تنها بر اساس کلیک‌های گذشته، بلکه بر اساس درک عمیق از سلیقه، کیفیت محتوا و رضایت بلندمدت شما ساخته شده است.
-------------------------------------------

نمای کلی ساختار پوشه‌ها و فایل‌ها
mtl_timeline_project/
│
├── .env                  # فایل متغیرهای محیطی (مثل کلیدهای API و رمزها)
├── .gitignore            # فایل‌هایی که نباید در گیت ثبت شوند
├── Dockerfile            # دستورالعمل ساخت ایمیج داکر برای استقرار
├── README.md             # توضیحات کلی پروژه و راهنمای راه‌اندازی
├── requirements.txt      # لیست کتابخانه‌های پایتون مورد نیاز پروژه
├── main.py               # نقطه ورود اصلی برای اجرای سرویس API
│
├── config/
│   ├── __init__.py
│   └── settings.py       # تنظیمات مرکزی پروژه (آدرس دیتابیس‌ها، مسیر مدل‌ها)
│
├── data_processing/
│   ├── __init__.py
│   └── offline_jobs.py   # اسکریپت‌های پردازش دسته‌ای داده‌ها برای آموزش مدل
│
├── notebooks/
│   ├── 1_data_exploration.ipynb
│   └── 2_model_prototyping.ipynb
│
├── saved_models/
│   ├── ranker_model.pkl
│   └── rgcn_model.pth
│
├── mtl_app/
│   ├── __init__.py
│   ├── api.py            # تعریف اندپوینت‌های API (مثلاً /timeline)
│   ├── orchestrator.py   # تابع اصلی generate_timeline که لایه‌ها را فراخوانی می‌کند
│   │
│   ├── tiers/
│   │   ├── __init__.py
│   │   ├── tier1_preprocessing.py
│   │   ├── tier2_graph_embedding.py
│   │   ├── tier3_ranking.py
│   │   ├── tier4_llm_reranking.py
│   │   ├── tier5_rl_policy.py
│   │   └── tier6_safety_filter.py
│   │
│   ├── training/
│   │   ├── __init__.py
│   │   ├── train_ranker.py
│   │   └── train_graph_model.py
│   │
│   └── utils/
│       ├── __init__.py
│       ├── caching.py    # توابع مربوط به کشینگ با Redis
│       └── logging.py    # تنظیمات مربوط به لاگ‌گیری
│
└── tests/
    ├── __init__.py
    ├── test_api.py
    └── test_tiers.py
توضیح هر فایل و پوشه
📁 پوشه ریشه (mtl_timeline_project/)
README.md: سند اصلی پروژه. شامل توضیحاتی درباره هدف پروژه، نحوه نصب نیازمندی‌ها (pip install -r requirements.txt) و چگونگی اجرای آن است.

requirements.txt: لیستی از تمام کتابخانه‌های پایتون که پروژه به آن‌ها نیاز دارد (مانند fastapi, torch, lightgbm, transformers). این فایل تضمین می‌کند که همه توسعه‌دهندگان از نسخه‌های یکسانی استفاده می‌کنند.

main.py: نقطه شروع برنامه. این فایل یک وب سرور (مثلاً با FastAPI) را راه‌اندازی می‌کند و اندپوینت‌های تعریف شده در mtl_app/api.py را در دسترس قرار می‌دهد.

Dockerfile: یک فایل متنی که به Docker می‌گوید چگونه یک ایمیج (Image) از برنامه بسازد. این ایمیج شامل تمام کدها، نیازمندی‌ها و تنظیمات لازم برای اجرای برنامه در هر محیطی است.

.env: برای نگهداری اطلاعات حساس مانند کلیدهای API، رمز عبور دیتابیس‌ها و... . این فایل هرگز در سیستم‌های ورژن کنترل (مثل گیت) ثبت نمی‌شود.

.gitignore: به گیت می‌گوید که کدام فایل‌ها و پوشه‌ها را نادیده بگیرد (مثلاً پوشه __pycache__ یا فایل .env).

📁 config/
settings.py: این فایل به جای پراکنده کردن تنظیمات در سراسر کد، همه آن‌ها را در یک مکان مرکزی نگهداری می‌کند. مواردی مانند آدرس URL پایگاه داده، مسیر ذخیره مدل‌ها، و کلیدهای API در این فایل تعریف می‌شوند.

📁 data_processing/
offline_jobs.py: شامل اسکریپت‌هایی است که برای پردازش‌های سنگین و دسته‌ای (Batch) داده‌ها استفاده می‌شوند. برای مثال، آماده‌سازی دیتاست‌های بزرگ برای آموزش مدل‌های رتبه‌بندی یا گراف از لاگ‌های خام در این بخش انجام می‌شود.

📁 notebooks/
این پوشه محل نگهداری Jupyter Notebook هاست. نوت‌بوک‌ها برای تحلیل اکتشافی داده‌ها (EDA)، آزمایش سریع ایده‌ها، و ساخت نمونه‌های اولیه (Prototype) مدل‌ها بسیار مفید هستند. این کدها معمولاً برای تولید نهایی استفاده نمی‌شوند.

📁 saved_models/
مدل‌های آموزش‌دیده در این پوشه ذخیره می‌شوند تا در زمان اجرا (Inference) بارگذاری و استفاده شوند. برای مثال، فایل مدل LightGBM با فرمت .pkl یا مدل PyTorch با فرمت .pth.

📁 mtl_app/ (پکیج اصلی برنامه)
این پوشه قلب پروژه و حاوی منطق اصلی الگوریتم است.

api.py: مسئول تعریف مسیرهای (Routes) API است. برای مثال، یک اندپوینت به نام /v1/timeline/{user_id} در این فایل تعریف می‌شود که درخواست‌های ورودی را دریافت کرده و به تابع ارکستراتور پاس می‌دهد.

orchestrator.py: این فایل شامل مهم‌ترین تابع پروژه یعنی generate_timeline() است. این تابع مانند یک رهبر ارکستر عمل کرده و به ترتیب، توابع مربوط به هر لایه (Tier) را فراخوانی می‌کند تا تایم‌لاین نهایی را تولید کند.

📁 tiers/: هر فایل در این پوشه، مسئول پیاده‌سازی یکی از لایه‌های الگوریتم MTL است. این کار باعث ماژولار شدن کد می‌شود.

tier1_preprocessing.py: توابع پاکسازی داده و استخراج ویژگی.

tier2_graph_embedding.py: منطق بارگذاری مدل گراف و دریافت embedding.

tier3_ranking.py: منطق تولید کاندیدا و نمره‌دهی با مدل رنکر.

tier4_llm_reranking.py: توابع مربوط به ارسال درخواست به LLM و پردازش پاسخ.

tier5_rl_policy.py: اعمال سیاست یادگیری تقویتی برای تنظیم نهایی ترتیب.

tier6_safety_filter.py: فیلتر نهایی محتوای مضر و اعمال قوانین عدالت.

📁 training/: اسکریپت‌های مربوط به آموزش مدل‌ها در این پوشه قرار می‌گیرند. این اسکریپت‌ها داده‌های پردازش‌شده را از data_processing خوانده و مدل‌ها را آموزش می‌دهند و در پوشه saved_models ذخیره می‌کنند.

📁 utils/: شامل توابع کمکی است که در بخش‌های مختلف پروژه استفاده می‌شوند.

caching.py: توابع اتصال به Redis و ذخیره/بازیابی داده از کش.

logging.py: تنظیمات مربوط به ثبت لاگ‌ها برای دیباگ و مانیتورینگ.

📁 tests/
این پوشه برای نوشتن تست‌های خودکار (Unit Tests و Integration Tests) است. تست‌نویسی تضمین می‌کند که با ایجاد تغییرات جدید، عملکرد قبلی سیستم دچار مشکل نشود.

test_tiers.py: تست عملکرد هر لایه به صورت مجزا.

test_api.py: تست کامل اندپوینت API برای اطمینان از صحت پاسخ نهایی.